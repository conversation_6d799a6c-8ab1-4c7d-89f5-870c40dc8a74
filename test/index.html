<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workout Agent - Function Tester</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .function-card {
            transition: all 0.3s ease;
        }
        .function-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .loading {
            display: none;
        }
        .loading.active {
            display: inline-block;
        }
        .result-container {
            max-height: 400px;
            overflow-y: auto;
        }
        .auth-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .test-input {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            transition: border-color 0.3s ease;
        }
        .test-input:focus {
            border-color: #3b82f6;
            outline: none;
            background: white;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Auth Status -->
    <div class="auth-status">
        <div id="authStatus" class="bg-white rounded-lg shadow-lg p-4 border-l-4 border-red-500">
            <div class="flex items-center">
                <i class="fas fa-user-slash text-red-500 mr-2"></i>
                <span class="text-sm font-medium text-gray-700">Not Authenticated</span>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <i class="fas fa-dumbbell text-blue-600 text-2xl mr-3"></i>
                    <h1 class="text-3xl font-bold text-gray-900">Workout Agent Tester</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="signInBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-envelope mr-2"></i>Sign In
                    </button>
                    <button id="signOutBtn" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors hidden">
                        <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- User Info Section -->
        <div id="userInfo" class="bg-white rounded-lg shadow-sm p-6 mb-8 hidden">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
                <i class="fas fa-user text-blue-600 mr-2"></i>User Information
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">User ID</label>
                    <input type="text" id="userId" class="test-input w-full" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input type="text" id="userEmail" class="test-input w-full" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                    <input type="text" id="userDisplayName" class="test-input w-full" readonly>
                </div>
            </div>
        </div>

        <!-- Function Tests Grid -->
        <div id="functionsGrid" class="grid grid-cols-1 lg:grid-cols-2 gap-8 hidden">
            <!-- Complete Onboarding -->
            <div class="function-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-user-plus text-green-600 text-xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Complete Onboarding</h3>
                </div>
                <p class="text-gray-600 mb-4">Creates a personalized fitness guide and first workout for new users.</p>
                <button onclick="testCompleteOnboarding()" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-play mr-2"></i>Test Function
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
                <div class="result-container mt-4 hidden">
                    <h4 class="font-medium text-gray-900 mb-2">Result:</h4>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"></pre>
                </div>
            </div>

            <!-- Generate Fitness Guide -->
            <div class="function-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-book text-blue-600 text-xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Generate Fitness Guide</h3>
                </div>
                <p class="text-gray-600 mb-4">Creates a personalized fitness guide based on user profile.</p>
                <button onclick="testGenerateFitnessGuide()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-play mr-2"></i>Test Function
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
                <div class="result-container mt-4 hidden">
                    <h4 class="font-medium text-gray-900 mb-2">Result:</h4>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"></pre>
                </div>
            </div>

            <!-- Create First Workout -->
            <div class="function-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-dumbbell text-purple-600 text-xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Create First Workout</h3>
                </div>
                <p class="text-gray-600 mb-4">Generates an initial workout plan for new users.</p>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Guide ID (optional)</label>
                    <input type="text" id="guideId" class="test-input w-full" placeholder="Leave empty to auto-generate">
                </div>
                <button onclick="testCreateFirstWorkout()" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-play mr-2"></i>Test Function
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
                <div class="result-container mt-4 hidden">
                    <h4 class="font-medium text-gray-900 mb-2">Result:</h4>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"></pre>
                </div>
            </div>

            <!-- Recommend Next Exercise -->
            <div class="function-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-lightbulb text-yellow-600 text-xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Recommend Next Exercise</h3>
                </div>
                <p class="text-gray-600 mb-4">Gets real-time exercise recommendations during workouts.</p>
                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="saveWorkout" class="mr-2">
                        <span class="text-sm text-gray-700">Save workout after recommendation</span>
                    </label>
                </div>
                <button onclick="testRecommendNextExercise()" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-play mr-2"></i>Test Function
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
                <div class="result-container mt-4 hidden">
                    <h4 class="font-medium text-gray-900 mb-2">Result:</h4>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"></pre>
                </div>
            </div>

            <!-- Generate Next Workout -->
            <div class="function-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-forward text-indigo-600 text-xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Generate Next Workout</h3>
                </div>
                <p class="text-gray-600 mb-4">Creates progressive workouts based on workout history.</p>
                <div class="space-y-3 mb-4">
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="skipRecoveryCheck" class="mr-2">
                            <span class="text-sm text-gray-700">Skip recovery check</span>
                        </label>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Target Muscles (comma-separated)</label>
                        <input type="text" id="targetMuscles" class="test-input w-full" placeholder="chest, shoulders, triceps">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Workout Type</label>
                        <select id="workoutType" class="test-input w-full">
                            <option value="">Any</option>
                            <option value="strength">Strength</option>
                            <option value="cardio">Cardio</option>
                            <option value="hiit">HIIT</option>
                            <option value="flexibility">Flexibility</option>
                        </select>
                    </div>
                </div>
                <button onclick="testGenerateNextWorkout()" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-play mr-2"></i>Test Function
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
                <div class="result-container mt-4 hidden">
                    <h4 class="font-medium text-gray-900 mb-2">Result:</h4>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"></pre>
                </div>
            </div>

            <!-- Analyze Last Workout -->
            <div class="function-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-chart-line text-red-600 text-xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Analyze Last Workout</h3>
                </div>
                <p class="text-gray-600 mb-4">Analyzes performance and provides insights on completed workouts.</p>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Workout ID (optional)</label>
                    <input type="text" id="workoutId" class="test-input w-full" placeholder="Leave empty to analyze last workout">
                </div>
                <button onclick="testAnalyzeLastWorkout()" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-play mr-2"></i>Test Function
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
                <div class="result-container mt-4 hidden">
                    <h4 class="font-medium text-gray-900 mb-2">Result:</h4>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"></pre>
                </div>
            </div>

            <!-- Fitness Chat -->
            <div class="function-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-comments text-teal-600 text-xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Fitness Chat</h3>
                </div>
                <p class="text-gray-600 mb-4">Chat with the AI fitness coach for personalized advice and guidance.</p>
                <div class="space-y-3 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                        <textarea id="chatMessage" class="test-input w-full h-20 resize-none" placeholder="Ask me anything about fitness, nutrition, or workouts..."></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Conversation ID (optional)</label>
                        <input type="text" id="conversationId" class="test-input w-full" placeholder="Leave empty for new conversation">
                    </div>
                </div>
                <button onclick="testFitnessChat()" class="w-full bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-play mr-2"></i>Send Message
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
                <div class="result-container mt-4 hidden">
                    <h4 class="font-medium text-gray-900 mb-2">AI Response:</h4>
                    <pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"></pre>
                </div>
            </div>
        </div>

        <!-- Utility Functions Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 mt-8 hidden" id="utilitySection">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">
                <i class="fas fa-tools text-gray-600 mr-2"></i>Utility Functions
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button onclick="createSampleUserProfile()" class="bg-gray-600 hover:bg-gray-700 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-user-plus mr-2"></i>Create Sample Profile
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
                <button onclick="checkUserProfile()" class="bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-search mr-2"></i>Check User Profile
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
                <button onclick="clearAllResults()" class="bg-yellow-600 hover:bg-yellow-700 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-eraser mr-2"></i>Clear All Results
                </button>
                <button onclick="testAllFunctions()" class="bg-purple-600 hover:bg-purple-700 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                    <i class="fas fa-play-circle mr-2"></i>Test All Functions
                </button>
            </div>
            <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                <h3 class="font-medium text-gray-900 mb-2">Keyboard Shortcuts:</h3>
                <div class="text-sm text-gray-600 space-y-1">
                    <div><kbd class="bg-gray-200 px-2 py-1 rounded">Ctrl/Cmd + Enter</kbd> - Test all functions</div>
                    <div><kbd class="bg-gray-200 px-2 py-1 rounded">Ctrl/Cmd + R</kbd> - Clear all results</div>
                </div>
            </div>
        </div>

        <!-- Authentication Required Message -->
        <div id="authRequired" class="text-center py-16">
            <i class="fas fa-lock text-gray-400 text-6xl mb-4"></i>
            <h2 class="text-2xl font-semibold text-gray-900 mb-2">Authentication Required</h2>
            <p class="text-gray-600 mb-6">Please sign in to test the workout agent functions.</p>

            <!-- Sign In Form -->
            <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Sign In</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" id="signInEmail" class="test-input w-full" placeholder="<EMAIL>" value="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input type="password" id="signInPassword" class="test-input w-full" placeholder="Password" value="testpassword123">
                        </div>
                    </div>
                    <button onclick="signInWithEmail()" class="w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                        <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                        <i class="fas fa-spinner fa-spin loading ml-2"></i>
                    </button>
                </div>

                <div class="border-t pt-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Create Test Account</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" id="signUpEmail" class="test-input w-full" placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input type="password" id="signUpPassword" class="test-input w-full" placeholder="Choose a password (min 6 chars)">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                            <input type="text" id="signUpDisplayName" class="test-input w-full" placeholder="Your Name">
                        </div>
                    </div>
                    <button onclick="signUpWithEmail()" class="w-full mt-4 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                        <i class="fas fa-user-plus mr-2"></i>Create Account
                        <i class="fas fa-spinner fa-spin loading ml-2"></i>
                    </button>
                </div>

                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h4 class="font-medium text-blue-900 mb-2">Quick Test Account</h4>
                    <p class="text-sm text-blue-700">Use the pre-filled credentials above for instant testing, or create your own account.</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Firebase SDKs -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, onAuthStateChanged, signInWithEmailAndPassword, createUserWithEmailAndPassword, updateProfile, signOut } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDG5SmBVzfEkNabJ6dy-shXGs1eXERmBoc",
            authDomain: "po2vf2ae7tal9invaj7jkf4a06hsac.firebaseapp.com",
            projectId: "po2vf2ae7tal9invaj7jkf4a06hsac",
            storageBucket: "po2vf2ae7tal9invaj7jkf4a06hsac.appspot.com",
            messagingSenderId: "116641458209502298474",
            appId: "1:116641458209502298474:web:your-app-id"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const functions = getFunctions(app);

        // Global variables
        window.auth = auth;
        window.functions = functions;
        window.currentUser = null;

        // Utility function to show loading state
        function setLoading(button, isLoading) {
            const spinner = button.querySelector('.loading');
            const icon = button.querySelector('i:not(.loading)');

            if (isLoading) {
                if (spinner) spinner.classList.add('active');
                if (icon) icon.style.display = 'none';
                button.disabled = true;
            } else {
                if (spinner) spinner.classList.remove('active');
                if (icon) icon.style.display = 'inline';
                button.disabled = false;
            }
        }

        // Auth state observer
        onAuthStateChanged(auth, (user) => {
            window.currentUser = user;
            updateAuthUI(user);
        });

        // Update UI based on auth state
        function updateAuthUI(user) {
            const authStatus = document.getElementById('authStatus');
            const signInBtn = document.getElementById('signInBtn');
            const signOutBtn = document.getElementById('signOutBtn');
            const userInfo = document.getElementById('userInfo');
            const functionsGrid = document.getElementById('functionsGrid');
            const utilitySection = document.getElementById('utilitySection');
            const authRequired = document.getElementById('authRequired');

            if (user) {
                // User is signed in
                authStatus.className = 'bg-white rounded-lg shadow-lg p-4 border-l-4 border-green-500';
                authStatus.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-user-check text-green-500 mr-2"></i>
                        <span class="text-sm font-medium text-gray-700">Authenticated as ${user.email}</span>
                    </div>
                `;

                signInBtn.classList.add('hidden');
                signOutBtn.classList.remove('hidden');
                userInfo.classList.remove('hidden');
                functionsGrid.classList.remove('hidden');
                utilitySection.classList.remove('hidden');
                authRequired.classList.add('hidden');

                // Populate user info
                document.getElementById('userId').value = user.uid;
                document.getElementById('userEmail').value = user.email || '';
                document.getElementById('userDisplayName').value = user.displayName || '';
            } else {
                // User is signed out
                authStatus.className = 'bg-white rounded-lg shadow-lg p-4 border-l-4 border-red-500';
                authStatus.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-user-slash text-red-500 mr-2"></i>
                        <span class="text-sm font-medium text-gray-700">Not Authenticated</span>
                    </div>
                `;

                signInBtn.classList.remove('hidden');
                signOutBtn.classList.add('hidden');
                userInfo.classList.add('hidden');
                functionsGrid.classList.add('hidden');
                utilitySection.classList.add('hidden');
                authRequired.classList.remove('hidden');
            }
        }

        // Sign in with email/password
        window.signInWithEmail = async function() {
            const button = event.target;
            const email = document.getElementById('signInEmail').value.trim();
            const password = document.getElementById('signInPassword').value;

            if (!email || !password) {
                alert('Please enter both email and password');
                return;
            }

            setLoading(button, true);

            try {
                await signInWithEmailAndPassword(auth, email, password);
                console.log('Sign in successful');
            } catch (error) {
                console.error('Sign in error:', error);
                let errorMessage = 'Sign in failed: ';

                switch (error.code) {
                    case 'auth/user-not-found':
                        errorMessage += 'No account found with this email. Please create an account first.';
                        break;
                    case 'auth/wrong-password':
                        errorMessage += 'Incorrect password. Please try again.';
                        break;
                    case 'auth/invalid-email':
                        errorMessage += 'Invalid email address format.';
                        break;
                    case 'auth/too-many-requests':
                        errorMessage += 'Too many failed attempts. Please try again later.';
                        break;
                    default:
                        errorMessage += error.message;
                }

                alert(errorMessage);
            } finally {
                setLoading(button, false);
            }
        };

        // Sign up with email/password
        window.signUpWithEmail = async function() {
            const button = event.target;
            const email = document.getElementById('signUpEmail').value.trim();
            const password = document.getElementById('signUpPassword').value;
            const displayName = document.getElementById('signUpDisplayName').value.trim();

            if (!email || !password) {
                alert('Please enter both email and password');
                return;
            }

            if (password.length < 6) {
                alert('Password must be at least 6 characters long');
                return;
            }

            setLoading(button, true);

            try {
                const userCredential = await createUserWithEmailAndPassword(auth, email, password);

                // Update display name if provided
                if (displayName) {
                    await updateProfile(userCredential.user, {
                        displayName: displayName
                    });
                }

                console.log('Account created successfully');
                alert('Account created successfully! You are now signed in.');
            } catch (error) {
                console.error('Sign up error:', error);
                let errorMessage = 'Account creation failed: ';

                switch (error.code) {
                    case 'auth/email-already-in-use':
                        errorMessage += 'An account with this email already exists. Please sign in instead.';
                        break;
                    case 'auth/invalid-email':
                        errorMessage += 'Invalid email address format.';
                        break;
                    case 'auth/weak-password':
                        errorMessage += 'Password is too weak. Please choose a stronger password.';
                        break;
                    default:
                        errorMessage += error.message;
                }

                alert(errorMessage);
            } finally {
                setLoading(button, false);
            }
        };

        // Legacy sign in function (for header button)
        window.signIn = function() {
            // Scroll to auth form
            document.getElementById('authRequired').scrollIntoView({ behavior: 'smooth' });
        };

        // Sign out function
        window.signOut = async function() {
            try {
                await signOut(auth);
            } catch (error) {
                console.error('Sign out error:', error);
                alert('Sign out failed: ' + error.message);
            }
        };

        // Event listeners
        document.getElementById('signInBtn').addEventListener('click', window.signIn);
        document.getElementById('signOutBtn').addEventListener('click', window.signOut);
    </script>

    <!-- Test Functions Script -->
    <script src="test-functions.js"></script>
</body>
</html>

# Flutter Integration Troubleshooting Guide

## Common Issues and Solutions

### 1. Firestore Permission Denied Errors

**Error**: `[cloud_firestore/permission-denied] The caller does not have permission to execute the specified operation.`

**Solution**: Update your Firestore Security Rules in the Firebase Console:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow all authenticated users to read exercises
    match /exercises/{document=**} {
      allow read: if request.auth != null;
      allow write: if false; // Only admin through Firebase Admin SDK
    }
    
    // Allow users to read/write their own workout history
    match /workoutHistory/{historyId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Allow users to read/write their own workouts
    match /userWorkouts/{workoutId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Allow users to read/write their own fitness guides
    match /userFitnessGuides/{guideId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Allow users to read/write their own conversations
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Allow users to read/write conversation messages
    match /conversations/{conversationId}/messages/{messageId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == get(/databases/$(database)/documents/conversations/$(conversationId)).data.userId;
    }
    
    // Allow all authenticated users to read fitness knowledge (RAG)
    match /fitnessKnowledge/{document=**} {
      allow read: if request.auth != null;
      allow write: if false; // Only through admin SDK
    }
  }
}
```

### 2. Firebase Functions Connection Error

**Error**: `Firebase Functions error - Code: unknown, Message: Could not connect to the server.`

**Common Causes and Solutions**:

#### A. Check Firebase Initialization
Ensure Firebase is properly initialized in your Flutter app:

```dart
// main.dart
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart'; // Generated file

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  runApp(MyApp());
}
```

#### B. Verify Function Region
If your functions are deployed to a specific region, configure it:

```dart
// In your service class
FirebaseFunctions.instance.useFunctionsEmulator('localhost', 5001); // For local testing
// OR for production with specific region
FirebaseFunctions.instanceFor(region: 'us-central1');
```

#### C. Check Network Permissions (Android)
In `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
```

#### D. iOS App Transport Security
In `ios/Runner/Info.plist`, ensure you have:
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

#### E. Check Function Names
Ensure function names match exactly (case-sensitive):
```dart
// Correct
final callable = functions.httpsCallable('fitnessChat');

// Incorrect
final callable = functions.httpsCallable('fitnesschat'); // Wrong case
```

### 3. Authentication Issues

**Ensure user is authenticated before calling functions**:

```dart
// Check authentication state
final user = FirebaseAuth.instance.currentUser;
if (user == null) {
  throw Exception('User must be authenticated');
}

// Get fresh ID token if needed
final idToken = await user.getIdToken(true);
```

### 4. Debugging Steps

1. **Enable Firebase Debug Logging**:
   ```dart
   FirebaseFirestore.instance.settings = Settings(
     persistenceEnabled: true,
     cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
   );
   ```

2. **Test with Direct HTTP Request**:
   ```dart
   // Test if functions are reachable
   final response = await http.get(
     Uri.parse('https://us-central1-po2vf2ae7tal9invaj7jkf4a06hsac.cloudfunctions.net/health'),
   );
   print('Health check: ${response.statusCode}');
   ```

3. **Check Function Logs**:
   ```bash
   firebase functions:log --only fitnessChat
   ```

### 5. Common Flutter Setup Checklist

- [ ] Firebase Core initialized
- [ ] Firebase Auth configured
- [ ] Cloud Functions package added
- [ ] Correct Firebase project configuration
- [ ] Network permissions set
- [ ] User authenticated before function calls
- [ ] Firestore security rules updated
- [ ] Functions deployed to correct region

### 6. Test Code for Debugging

```dart
Future<void> testFirebaseConnection() async {
  try {
    // Test Auth
    final user = FirebaseAuth.instance.currentUser;
    print('Auth User: ${user?.uid}');
    
    // Test Firestore
    final testDoc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user?.uid)
        .get();
    print('Firestore accessible: ${testDoc.exists}');
    
    // Test Functions
    final functions = FirebaseFunctions.instance;
    final callable = functions.httpsCallable('health');
    final result = await callable.call();
    print('Functions accessible: ${result.data}');
    
  } catch (e) {
    print('Connection test error: $e');
  }
}
```

### 7. Firebase Functions URL Issue

If functions still can't connect, try using the direct URL:

```dart
// Instead of just the function name, use the full URL
final functions = FirebaseFunctions.instanceFor(
  region: 'us-central1',
);

// Or set a custom domain if needed
final callable = functions.httpsCallableFromUrl(
  'https://us-central1-po2vf2ae7tal9invaj7jkf4a06hsac.cloudfunctions.net/fitnessChat',
);
```

---

If issues persist after trying these solutions:
1. Check Firebase Console for function deployment status
2. Verify billing is enabled (required for external network requests)
3. Check function logs for any errors
4. Ensure all Firebase services are enabled in the console
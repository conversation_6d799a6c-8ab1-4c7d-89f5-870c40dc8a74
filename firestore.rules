rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid));
    }
    
    // Users collection - users can only access their own data
    match /users/{userId} {
      allow read: if isOwner(userId);
      allow create: if isOwner(userId);
      allow update: if isOwner(userId) && 
        request.resource.data.uid == userId; // Prevent changing uid
      allow delete: if false; // Prevent accidental deletion
    }
    
    // Exercises collection - read-only for all authenticated users
    match /exercises/{exerciseId} {
      allow read: if isAuthenticated();
      allow write: if false; // Admin only via server
    }
    
    // Exercise aliases - read-only for all authenticated users
    match /exerciseAliases/{alias} {
      allow read: if isAuthenticated();
      allow write: if false; // Admin only via server
    }
    
    // Workout plans - public plans readable by all, private by owner
    match /workoutPlans/{planId} {
      allow read: if resource.data.isPublic == true || 
        (isAuthenticated() && resource.data.createdBy == request.auth.uid);
      allow create: if isValidUser() && 
        request.resource.data.createdBy == request.auth.uid;
      allow update: if isAuthenticated() && 
        resource.data.createdBy == request.auth.uid;
      allow delete: if isAuthenticated() && 
        resource.data.createdBy == request.auth.uid;
    }
    
    // User workouts - private to user
    match /userWorkouts/{workoutId} {
      allow read: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid || 
         resource.data.isPublic == true);
      allow create: if isValidUser() && 
        request.resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() && 
        resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated() && 
        resource.data.userId == request.auth.uid;
    }
    
    // Workout history - private to user
    match /workoutHistory/{historyId} {
      allow read: if isAuthenticated() && 
        resource.data.userId == request.auth.uid;
      allow create: if isValidUser() && 
        request.resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() && 
        resource.data.userId == request.auth.uid &&
        resource.data.userId == request.resource.data.userId; // Can't change userId
      allow delete: if isAuthenticated() && 
        resource.data.userId == request.auth.uid;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}